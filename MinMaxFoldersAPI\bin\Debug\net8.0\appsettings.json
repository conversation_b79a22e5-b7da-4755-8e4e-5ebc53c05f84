{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "MinMaxFoldersAPI.Utils": "Error"}, "Console": {"FormatterName": "SystemdConsole", "FormatterOptions": {"TimestampFormat": "yyyy-MM-dd HH:mm:ss ", "UseColors": true, "LogLevelToColorMap": {"Error": "Red", "Warning": "Yellow", "Information": "White", "Debug": "<PERSON>"}}}, "File": {"Path": "logs/minmaxfolders-.txt", "Append": true, "FileSizeLimitBytes": 10485760, "MaxRollingFiles": 10}}, "AllowedHosts": "*"}