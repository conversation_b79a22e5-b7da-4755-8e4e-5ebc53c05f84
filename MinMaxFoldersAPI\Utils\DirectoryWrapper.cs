using Microsoft.Extensions.Logging;

namespace MinMaxFoldersAPI.Utils;

public class DirectoryWrapper : IDirectoryWrapper
{
    public bool Exists(string path)
    {
        var exists = Directory.Exists(path);
        if (!exists)
        {
            Console.Error.WriteLine($"Directory does not exist: {path}");
        }
        return exists;
    }

    public DirectoryInfo[] GetDirectories(string path)
    {   
        try
        {
            var dirs = new DirectoryInfo(path).GetDirectories();
            Console.WriteLine($"Successfully retrieved directories from: {path}");
            return dirs;
        }
        catch (Exception ex)
        {       
            Console.Error.WriteLine($"Error getting directories: {path}");
            Console.Error.WriteLine(ex.Message);
            throw;
        }
    }

    public long GetDirectorySize(DirectoryInfo directory)
    {   
        try
        {
            var size = directory.GetFiles("*", SearchOption.AllDirectories).Sum(file => file.Length) +
                      directory.GetDirectories().Sum(dir => GetDirectorySize(dir));
            
            return size;
        }
        catch (UnauthorizedAccessException ex)
        {
            Console.Error.WriteLine($"Unauthorized access when calculating size for directory: {directory.FullName}");
            Console.Error.WriteLine(ex.Message);
            throw;
        }
    }

    public void Move(string sourcePath, string destinationPath)
    {
        try
        {
            Directory.Move(sourcePath, destinationPath);
            Console.WriteLine($"Successfully moved directory from {sourcePath} to {destinationPath}");
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Failed to move directory from {sourcePath} to {destinationPath}");
            Console.Error.WriteLine(ex.Message);
            throw;
        }
    }
}
