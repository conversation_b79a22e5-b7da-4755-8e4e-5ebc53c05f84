namespace MinMaxFoldersAPI.Models;

public record FolderInfo
{
    public string Name { get; init; } = string.Empty;
    public long Size { get; init; }
    public string ReadableSize { get; init; } = string.Empty;
}

public record FolderInfoResponse
{
    public IEnumerable<FolderSummary> SmallestFolders { get; init; } = Enumerable.Empty<FolderSummary>();
    public IEnumerable<FolderSummary> LargestFolders { get; init; } = Enumerable.Empty<FolderSummary>();
}

public record FolderSummary
{
    public string Name { get; init; } = string.Empty;
    public string Size { get; init; } = string.Empty;
}

public record MoveRequest(string FromPath, string ToPath, string Name);