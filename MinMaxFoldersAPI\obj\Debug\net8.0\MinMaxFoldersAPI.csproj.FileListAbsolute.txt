C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\MinMaxFoldersAPI.exe
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\MinMaxFoldersAPI.deps.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\MinMaxFoldersAPI.runtimeconfig.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\MinMaxFoldersAPI.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\MinMaxFoldersAPI.pdb
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.AssemblyInfo.cs
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets\msbuild.MinMaxFoldersAPI.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets\msbuild.build.MinMaxFoldersAPI.props
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.MinMaxFoldersAPI.props
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.MinMaxFoldersAPI.props
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\scopedcss\bundle\MinMaxFoldersAPI.styles.css
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFo.26B0F2EC.Up2Date
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\refint\MinMaxFoldersAPI.dll
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.pdb
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\MinMaxFoldersAPI.genruntimeconfig.cache
C:\Users\<USER>\Documents\repo\MinMaxFoldersAPI\MinMaxFoldersAPI\obj\Debug\net8.0\ref\MinMaxFoldersAPI.dll
