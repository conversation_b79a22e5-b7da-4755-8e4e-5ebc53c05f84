using MinMaxFoldersAPI.Models;
using MinMaxFoldersAPI.Services;

namespace MinMaxFoldersAPI.Endpoints;

public static class FolderEndpoints
{
    public static void MapFolderEndpoints(this WebApplication app)
    {
        app.MapGet("/readFolders", (string path, int pageSize, IFolderService folderService) =>
        {
            try
            {
                return Results.Ok(folderService.GetFolderSizes(path, pageSize));
            }
            catch (Exception ex)
            {
                return Results.BadRequest(ex.Message);
            }
        })
        .WithName("ReadFolders")
        .WithOpenApi();

        app.MapPost("/moveFolder", (MoveRequest request, IFolderService folderService) =>
        {
            try
            {
                folderService.MoveFolder(request);
                return Results.Ok($"Folder {request.Name} moved successfully");
            }
            catch (Exception ex)
            {
                return Results.BadRequest(ex.Message);
            }
        })
        .WithName("MoveFolder")
        .WithOpenApi();
    }
}