using MinMaxFoldersAPI.Models;
using MinMaxFoldersAPI.Utils;

namespace MinMaxFoldersAPI.Services;

public class FolderService : IFolderService
{
    private readonly IDirectoryWrapper _directoryWrapper;
    private readonly ISizeConverter _sizeConverter;

    public FolderService(IDirectoryWrapper directoryWrapper, ISizeConverter sizeConverter)
    {
        _directoryWrapper = directoryWrapper;
        _sizeConverter = sizeConverter;
    }

    public FolderInfoResponse GetFolderSizes(string path, int pageSize)
    {
        if (!_directoryWrapper.Exists(path))
        {
            throw new DirectoryNotFoundException($"Directory {path} does not exist");
        }

        var folderSizes = _directoryWrapper.GetDirectories(path)
            .Select(dir => new FolderInfo
            {
                Name = dir.Name,
                Size = _directoryWrapper.GetDirectorySize(dir),
                ReadableSize = _sizeConverter.GetReadableSize(_directoryWrapper.GetDirectorySize(dir))
            })
            .ToList();

        return new FolderInfoResponse
        {
            SmallestFolders = folderSizes
                .OrderBy(f => f.Size)
                .Take(pageSize)
                .Select(f => new FolderSummary { Name = f.Name, Size = f.ReadableSize }),

            LargestFolders = folderSizes
                .OrderByDescending(f => f.Size)
                .Take(pageSize)
                .Select(f => new FolderSummary { Name = f.Name, Size = f.ReadableSize })
        };
    }

    public void MoveFolder(MoveRequest request)
    {
        var sourcePath = Path.Combine(request.FromPath, request.Name);
        var destinationPath = Path.Combine(request.ToPath, request.Name);

        if (!_directoryWrapper.Exists(sourcePath))
        {
            throw new DirectoryNotFoundException($"Source folder {sourcePath} does not exist");
        }

        if (!_directoryWrapper.Exists(request.ToPath))
        {
            throw new DirectoryNotFoundException($"Destination path {request.ToPath} does not exist");
        }

        if (_directoryWrapper.Exists(destinationPath))
        {
            throw new InvalidOperationException($"Folder {request.Name} already exists in destination path");
        }

        //_directoryWrapper.Move(sourcePath, destinationPath);
    }
}