using MinMaxFoldersAPI.Endpoints;
using MinMaxFoldersAPI.Services;
using MinMaxFoldersAPI.Utils;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register services
builder.Services.AddScoped<IFolderService, FolderService>();
builder.Services.AddScoped<IDirectoryWrapper, DirectoryWrapper>();
builder.Services.AddScoped<ISizeConverter, SizeConverter>();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Map endpoints
app.MapFolderEndpoints();

app.Run();
